services:
  app:
    container_name: ${APP_NAME}-backend
    build:
      context: .
      dockerfile: Dockerfile
      target: base
    env_file:
      - .env
      - .env.local
    volumes:
      - ${APP_VOLUME:-.}:/app
      - var:/app/var
      - caddy_data:/data
      - caddy_config:/config
    ports:
      # HTTP
      - target: 8080
        published: ${HTTP_PORT:-8000}
        protocol: tcp
      # HTTPS
      # - target: 443
      #   published: ${HTTPS_PORT:-443}
      #   protocol: tcp
      # # HTTP/3
      # - target: 443
      #   published: ${HTTP3_PORT:-443}
      #   protocol: udp
    restart: unless-stopped
    environment:
      MERCURE_EXTRA_DIRECTIVES: demo
      MERCURE_PUBLISHER_JWT_KEY: ${CADDY_MERCURE_JWT_SECRET:-!ChangeThisMercureHubJWTSecretKey!}
      MERCURE_SUBSCRIBER_JWT_KEY: ${CADDY_MERCURE_JWT_SECRET:-!ChangeThisMercureHubJWTSecretKey!}
      TRUSTED_PROXIES: ${TRUSTED_PROXIES:-*********/8,10.0.0.0/8,**********/12,***********/16}
      TRUSTED_HOSTS: ^${SERVER_NAME:-example\.com|localhost|127.0.0.1}|app$$
      PHP_IDE_CONFIG: serverName=${XDEBUG_SERVER_NAME:-lvpportal}
      XDEBUG_MODE: debug
    extra_hosts:
      - host.docker.internal:host-gateway
    tty: true
    networks: [ lvpportal_network ]

  postgres:
    image: postgres:17-alpine3.21@sha256:88e904502786eb3765bcbd9c73f371cfd00978f9cc4cd1402f2ca5b3d097013a
    env_file: [ { path: .env, required: true }, { path: .env.local, required: false } ]
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-lvp-portal}
      POSTGRES_USER: ${POSTGRES_USER:-user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-userpwd}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - ${POSTGRES_VOLUME:-.devcontainer/data/postdb}:/var/lib/postgresql/data
    ports:
      - '8001:5432'
    networks: [ lvpportal_network ]

  db:
    container_name: ${APP_NAME}-db
    image: mariadb:10.6
    command: --default-authentication-plugin=mysql_native_password
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PWD:-rootpwd}
      MYSQL_DATABASE: ${DB_NAME:-lvp-portal}
      MYSQL_USER: ${DB_USER:-user}
      MYSQL_PASSWORD: ${DB_USER_PWD:-userpwd}
    volumes:
      - ${DB_VOLUME:-.devcontainer/data/db}:/var/lib/mysql
    ports:
      - ${DB_PORT:-3306}:3306
    networks: [ lvpportal_network ]

  dbfiles:
    container_name: ${APP_NAME}-db-files
    image: mariadb:10.6
    command: --default-authentication-plugin=mysql_native_password
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PWD:-rootpwd}
      MYSQL_DATABASE: ${DB_NAME:-lvp-portal}
      MYSQL_USER: ${DB_USER:-user}
      MYSQL_PASSWORD: ${DB_USER_PWD:-userpwd}
    volumes:
      - ${DB_VOLUME:-.devcontainer/data/dbfiles}:/var/lib/mysql
    ports:
      - ${DB_PORT:-3307}:3307
    networks: [ lvpportal_network ]

  s3storage:
    image: quay.io/minio/minio:latest
    ports:
      - '8004:7550'
      - '8005:7551'
    env_file:
      - .env.local
    user: ${USER_UID:-1000}:${USER_GID:-1000}
    volumes:
      - .devcontainer/data/s3storage:/data
    command:
      - 'server'
      - '/data'
      - '--address'
      - ':7550'
      - '--console-address'
      - ':7551'
    networks: [ lvpportal_network ]

  mailcatcher:
    container_name: ${APP_NAME}-mailcatcher
    image: dockage/mailcatcher:0.9.0
    ports:
      - ${MAIL_CATCHER_PORT_UI:-1080}:1080
    networks: [ lvpportal_network ]


  # phpmyadmin:
  #   image: phpmyadmin/phpmyadmin
  #   restart: always
  #   environment:
  #     PMA_HOSTS: db,dbfiles
  #     PMA_PORT: 3306,3306
  #   ports:
  #     - 8080:80
  #   links:
  #     - db
  #     - dbfiles
  #   networks:
  #     - local

networks:
  lvpportal_network:
    driver: bridge
    name: lvpportal_network

volumes:
  var:
  caddy_data:
  caddy_config:
