name: Releases

on:
    push:
        branches:
            - master
            - release
    workflow_dispatch:

jobs:
    release-please:
        runs-on: prezero-github-runner
        outputs:
            release_created: ${{ steps.release.outputs.release_created }}
            tag_name: ${{ steps.release.outputs.tag_name }}
        steps:
            - uses: googleapis/release-please-action@c2a5a2bd6a758a0937f1ddb1e8950609867ed15c # v4
              id: release
              with:
                  token: ${{ secrets.PREZERO_GITHUB_TOKEN }}
                  target-branch: ${{ github.ref_name }}
