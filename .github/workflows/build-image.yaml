name: Build Release Image

on:
  release:
    types:
      - created

run-name: Build Release Image (${{ github.event.release.tag_name }})

jobs:
  build-release-images:
    name: Build release images
    uses: prezero/workflows/.github/workflows/docker-build.yaml@8dbacafaf67538f8afb5cac6281292f04462b73f # v1.38.0
    with:
      ENV: prod
      DOCKERFILE: Dockerfile
      IMAGE: ghcr.io/${{ github.repository }}
      TAG: ${{ github.event.release.tag_name }}
      RELEASE_CREATED: true
    secrets: inherit
