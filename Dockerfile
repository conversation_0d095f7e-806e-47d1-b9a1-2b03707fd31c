ARG IMAGE=ghcr.io/prezero/docker-images/franken:1.1.7
ARG IMAGE_DEV=ghcr.io/prezero/docker-images/franken:1.1.7-dev

# --- START BASE ---------------------------------------------------------------- #
# hadolint ignore=DL3006
FROM $IMAGE_DEV AS base

ENV SERVER_NAME=:8080

# --- END BASE ----------------------------------------------------------------- #

# --- START BUILD -------------------------------------------------------------- #
FROM base AS build

WORKDIR /app

ARG COMPOSER_AUTH
ENV COMPOSER_AUTH ${COMPOSER_AUTH}
ENV COMPOSER_ALLOW_SUPERUSER=1

COPY composer.* symfony.* package.* yarn.lock .npmrc ./

RUN set -eux; \
	composer install --no-cache --prefer-dist --no-dev --no-autoloader --no-scripts --no-progress;

RUN --mount=type=secret,id=PREZERO_NPM_TOKEN \
    sed -i "s/\${PREZERO_NPM_TOKEN}/'$(cat /run/secrets/PREZERO_NPM_TOKEN)'/g" /app/.npmrc; \
    cp /app/.npmrc /tmp/.npmrc

RUN set -eux; \
    yarn install --prefer-offline --frozen-lockfile; \
    yarn cache clean;

# copy sources
COPY --link .. ./
#COPY docker/config/php/conf.d/custom_config.ini $PHP_INI_DIR/conf.d/

RUN set -eux; \
    cp /tmp/.npmrc /app/.npmrc; \
    yarn build; \
    rm -rf node_modules; \
    rm /app/.npmrc

# --- END BUILD ---------------------------------------------------------------- #

# --- START PROD IMAGE --------------------------------------------------------- #
# hadolint ignore=DL3006
FROM $IMAGE AS prod

LABEL org.opencontainers.image.source="https://github.com/prezero/lvpportal-sf"
LABEL org.opencontainers.image.description="PreZero lvpportal-sf Image"
WORKDIR /app

# Disable worker mode
ENV FRANKENPHP_CONFIG=""
# Disable auto https
ENV SERVER_NAME=:8080

# Copy dependencies
COPY --chown=www-data:www-data --from=build /app .
#COPY docker/config/php/conf.d/custom_config.ini $PHP_INI_DIR/conf.d/

RUN set -eux; \
	mkdir -p var/cache var/log; \
    composer dump-autoload --classmap-authoritative --no-dev; \
    composer dump-env prod; \
    composer run-script --no-dev post-install-cmd; \
	chmod +x bin/console; \
    chown -R www-data:www-data /app; \
    sync;
# --- END PROD IMAGE ----------------------------------------------------------- #
