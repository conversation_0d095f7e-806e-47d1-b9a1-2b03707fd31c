# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices/configuration.html#application-related-configuration
parameters:
  sap_api_url: '%env(SAP_API_URL)%' # from .env file
  sap_api_user: '%env(SAP_API_USER)%'
  sap_api_password: '%env(SAP_API_PASSWORD)%'
  app.fixtures_pass: '%env(string:FIXTURES_PASS)%'
  app.language_switch_enabled: '%env(bool:APP_LANGUAGE_SWITCH)%'

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
            - '../src/Migrations/'
            - '../src/Tests/'

    # controllers are imported separately to make sure services can be injected
    # as action arguments even if you don't extend any base controller class
    App\Controller\:
        resource: '../src/Controller/'
        tags: ['controller.service_arguments']

    # explicitly configure the service
    App\Services\Mailer:
      arguments:
        $emailFrom: '%env(EMAIL_FROM)%'
        $emailFromName: '%env(EMAIL_FROM_NAME)%'
        $emailReplyTo: '%env(EMAIL_REPLY_TO)%'
        $mailBaselink: '%env(MAIL_BASE_LINK)%'
        $emailManager: '%env(csv:EMAIL_MANAGER)%'
        $emailOrder: '%env(csv:EMAIL_ORDER)%'
        $emailStorno: '%env(csv:EMAIL_STORNO)%'
        $emailQuestion: '%env(csv:EMAIL_QUESTION)%'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    App\EventSubscriber\LocaleSubscriber:
      arguments:
        $languageSwitchEnabled: '%app.language_switch_enabled%'
      tags:
        - { name: kernel.event_subscriber}
    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
