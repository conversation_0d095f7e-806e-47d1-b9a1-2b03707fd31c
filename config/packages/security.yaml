security:
    password_hashers:
        Symfony\Component\Security\Core\User\InMemoryUser: plaintext
        App\Entity\Main\User:
            algorithm: "auto"
            migrate_from:
                - bcrypt

    # https://symfony.com/doc/current/security.html#where-do-users-come-from-user-providers
    providers:
        # used to reload user from session & other features (e.g. switch_user)
        app_user_provider:
            entity:
                class: App\Entity\Main\User
                property: email
        in_memory:
          memory:
            users:
              '%env(API_USER)%':
                password: '%env(API_PASSWORD)%'
                roles: 'ROLE_API'
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        api:
            pattern: ^/(api|rest)/
            stateless: true
            http_basic: true
            provider: in_memory
        main:
            lazy: true
            provider: app_user_provider

            # activate different ways to authenticate
            # https://symfony.com/doc/current/security.html#firewalls-authentication

            # https://symfony.com/doc/current/security/impersonating_user.html
            # switch_user: true
            custom_authenticator:
                - App\Security\LoginFormAuthenticator
            logout:
                path: app_logout

    role_hierarchy:
      ROLE_ADMIN: ['ROLE_MANAGER']
      ROLE_MANAGER: ['ROLE_REPORT', 'ROLE_DOCUMENTS', 'ROLE_ORDER']
      ROLE_REPORT: ['ROLE_USER']

      ROLE_ORDER: ['ROLE_USER']
      ROLE_DOCUMENTS: ['ROLE_USER']
      ROLE_USER: ['ROLE_USER']

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/admin, roles: [ROLE_ADMIN] }
        - { path: ^/settings, roles: [ROLE_ADMIN, ROLE_MANAGER] }
        - { path: ^/management/order, roles: [ROLE_REPORT] }
        - { path: ^/management, roles: [ROLE_ADMIN, ROLE_MANAGER, ROLE_REPORT] }
        - { path: ^/api, roles: [ROLE_API, ROLE_ADMIN] }
        - { path: ^/rest, roles: ROLE_API }
        - { path: ^/contact, roles: PUBLIC_ACCESS }
        - { path: ^/data-privacy, roles: PUBLIC_ACCESS }
        - { path: ^/imprint, roles: PUBLIC_ACCESS }
        - { path: ^/compliance, roles: PUBLIC_ACCESS }
        - { path: ^/login, roles: PUBLIC_ACCESS }
        - { path: ^/passwordreset, roles: PUBLIC_ACCESS }
        - { path: ^/newpassword, roles: PUBLIC_ACCESS }
        - { path: ^/documents, roles: [ROLE_DOCUMENTS] }
        - { path: ^/order, roles: [ROLE_ORDER] }
        - { path: ^/, roles: ROLE_USER }
