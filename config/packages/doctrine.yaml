# config/packages/doctrine.yaml
doctrine:
  dbal:
    default_connection: default
    connections:
      default:
        url: '%env(resolve:DATABASE_URL)%'
        server_version: '17'
        logging: true
        profiling_collect_backtrace: '%kernel.debug%'
        use_savepoints: true
      files:
        url: '%env(resolve:DATABASE_URL_FILES)%'
        server_version: 'mariadb-10.9.3'
    types:
      uuid: Ramsey\Uuid\Doctrine\UuidType
  orm:
    enable_lazy_ghost_objects: true
    report_fields_where_declared: true
    naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware

    identity_generation_preferences:
      Doctrine\DBAL\Platforms\PostgreSQLPlatform: identity

    auto_mapping: true
    mappings:
      App:
        type: attribute
        is_bundle: false
        dir: '%kernel.project_dir%/src/Entity'
        prefix: 'App\Entity'
        alias: App

    controller_resolver:

      auto_mapping: true


when@test:
  doctrine:
    dbal:
      dbname_suffix: '_test%env(default::TEST_TOKEN)%'

when@prod:
  doctrine:
    orm:
      auto_generate_proxy_classes: false
      proxy_dir: '%kernel.build_dir%/doctrine/orm/Proxies'
      query_cache_driver:
        type: pool
        pool: doctrine.system_cache_pool
      result_cache_driver:
        type: pool
        pool: doctrine.result_cache_pool

  framework:
    cache:
      pools:
        doctrine.result_cache_pool:
          adapter: cache.app
        doctrine.system_cache_pool:
          adapter: cache.system
