{"devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "@symfony/webpack-encore": "^4.6.1", "@tailwindcss/forms": "^0.5.7", "autoprefixer": "^10.4.7", "core-js": "^3.22.3", "file-loader": "^6.0.0", "postcss": "^8.4.35", "postcss-loader": "^7.0.0", "regenerator-runtime": "^0.14.1", "sass": "^1.75.0", "tailwindcss": "^3.4.1", "webpack-cli": "^5.1.4", "webpack-notifier": "^1.6.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.1.1", "@popperjs/core": "^2.11.8", "@prezero/blackgrid": "^0.8.0", "bootstrap": "^4.5.2", "copy-webpack-plugin": "^12.0.2", "jquery": "^3.6.0", "noty": "^3.2.0-beta-deprecated", "postcss-import": "^16.0.0", "sass-loader": "^14.1.0", "webpack": "^5.90.1"}}