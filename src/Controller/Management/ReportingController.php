<?php

declare(strict_types=1);

namespace App\Controller\Management;

use App\Dto\ReportingCollectingPlaceDto;
use App\Dto\ReportingContractAreaDto;
use App\Dto\ReportingUserDto;
use App\Entity\Main\CollectingPlace;
use App\Entity\Main\ContractArea;
use App\Entity\Main\User;
use App\Repository\Main\ContractAreaRepository;
use App\Services\AccessHelper;
use App\Services\CollectingPlaceHelper;
use App\Services\ContractAreaHelper;
use AutoMapperPlus\AutoMapperInterface;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class ReportingController extends BaseCrudController
{
    private readonly EntityManagerInterface $manager;

    protected AccessHelper $accessHelper;

    public function __construct(
        EntityManagerInterface $em,
        AccessHelper $accessHelper,
        ContractAreaHelper $contractAreaHelper,
        CollectingPlaceHelper $collectingPlaceHelper,
        SerializerInterface $serializer,
        private readonly AutoMapperInterface $autoMapper,
        ContractAreaRepository $repository,
        private readonly TranslatorInterface $translator,
    ) {
        parent::__construct($repository, $serializer, $em, $accessHelper, $contractAreaHelper, $collectingPlaceHelper, 'ReportingController', 'management-neu/reporting/list.html.twig', 'management-neu/reporting/details.html.twig');
        $this->manager = $em;
        $this->accessHelper = $accessHelper;
        $this->contractAreaHelper = $contractAreaHelper;
        $this->collectingPlaceHelper = $collectingPlaceHelper;
        $this->serializer = $serializer;
    }

    protected function mapEntityToDto($entity, $request)
    {
        $self = $this;

        /** @phpstan-ignore-next-line */
        return $this->autoMapper->map($entity, $request, [
            'generateUrl' => fn (string $route, array $params): string => $self->generateUrl(route: $route, parameters: $params),
        ]);
    }

    protected function mapDtoToEntity($dto, $entity, Request $request)
    {
        // return $this->autoMapper->mapToObject($dto, $entity, [
        //    'contractAreaRepository' => $this->contractRepository
        // ]);
    }

    protected function getContractAreaOptions($entityList, array $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'status' => ['visible' => true, 'label' => $this->translator->trans('status')],
            'name' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.contract_area')],
            'state' => ['visible' => true, 'label' => $this->translator->trans('contractArea.field.state')],
            'validFrom' => ['visible' => true, 'label' => $this->translator->trans('contractArea.field.validFrom')],
            'validTo' => ['visible' => true, 'label' => $this->translator->trans('contractArea.field.validTo')],
            'countCollectingPlace' => ['visible' => true, 'label' => $this->translator->trans('count_collecting_place')],
            'countUser' => ['visible' => true, 'label' => $this->translator->trans('count_user')],
            'detailLink' => ['visible' => false, 'label' => 'detailLink'],
        ];

        return $options;
    }

    protected function getUserOptions($entityList, array $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'status' => ['visible' => true, 'label' => $this->translator->trans('order.status')],
            'email' => ['visible' => true, 'label' => $this->translator->trans('contact.sender_email')],
            'assignedRoles' => ['visible' => true, 'label' => $this->translator->trans('roles_title')],
            'countContractArea' => ['visible' => true, 'label' => $this->translator->trans('count_contract_area')],
            'countCollectingPlace' => ['visible' => true, 'label' => $this->translator->trans('count_collecting_place')],
        ];

        return $options;
    }

    protected function getCollectingPlaceOptions($entityList, array $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'status' => ['visible' => true, 'label' => $this->translator->trans('order.status')],
            'dsdId' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.field.dsdid')],
            'esaId' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.field.esaid')],
            'name' => ['visible' => true, 'label' => $this->translator->trans('name')],
            'street' => ['visible' => true, 'label' => $this->translator->trans('street')],
            'houseNumber' => ['visible' => true, 'label' => $this->translator->trans('house_number')],
            'postalCode' => ['visible' => true, 'label' => $this->translator->trans('postal_code')],
            'city' => ['visible' => true, 'label' => $this->translator->trans('city')],
            'district' => ['visible' => true, 'label' => $this->translator->trans('district')],
            'countContractArea' => ['visible' => true, 'label' => $this->translator->trans('count_contract_area')],
            'countUser' => ['visible' => true, 'label' => $this->translator->trans('count_user')],
            'detailLink' => ['visible' => false, 'label' => 'detailLink'],
        ];

        return $options;
    }

    #[Route(path: '/management/reporting', name: 'management_reporting')]
    public function managementReporting(): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_REPORT')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->redirectToRoute(route: 'management_reporting_contractArea');
    }

    #[Route(path: '/management/reporting/contractArea', name: 'management_reporting_contractArea')]
    public function managementReportingContractArea(): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_REPORT')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }
        $self = $this;
        $items = $this->manager->getRepository(ContractArea::class)->findAll();
        $mappedList = array_map(callback: fn (ContractArea $entity) => $self->mapEntityToDto(entity: $entity, request: ReportingContractAreaDto::class), array: $items);

        return $this->render(view: 'management-neu/reporting/list.html.twig', parameters: $this->getContractAreaOptions(entityList: $items, options: [
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/reporting/contractArea/{uuid}', name: 'management_reporting_contractArea_details')]
    public function managementReportingContractAreaDetails($uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_REPORT')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->redirectToRoute(route: 'management_reporting_contractArea_details_collectingPlaces', parameters: ['uuid' => $uuid]);
    }

    #[Route(path: '/management/reporting/contractArea/{uuid}/collectingPlaces', name: 'management_reporting_contractArea_details_collectingPlaces')]
    public function managementReportingContractAreaDetailsCollectingPlaces($uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        /** @var ContractArea $contractArea */
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $items = $this->contractAreaHelper->getCollectingPlaceList(contractArea: $contractArea);

        $mappedList = array_map(callback: fn ($entity) => $self->mapEntityToDto(entity: $entity, request: ReportingCollectingPlaceDto::class), array: $items);

        return $this->render(view: 'management-neu/reporting/details.html.twig', parameters: $this->getCollectingPlaceOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_reporting_contractArea'),
            'contractArea' => $contractArea,
            'object' => ['name' => 'Vertragsgebiete'],
            'title' => 'Vertragsgebiet '.$contractArea->getName(),
            'links' => [
                [
                    'name' => 'Umschläge',
                    'path' => 'management_reporting_contractArea_details_collectingPlaces',
                    'active' => true,
                ],
                [
                    'name' => 'Benutzer',
                    'path' => 'management_reporting_contractArea_details_users',
                    'active' => false,
                ],
            ],
            'table' => [
                'name' => 'Umschläge',
                'text' => $this->translator->trans('collectingplace.collecting_places_contract_area_text', ['%name%' => $contractArea->getName()]),
            ],
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/reporting/contractArea/{uuid}/users', name: 'management_reporting_contractArea_details_users')]
    public function managementReportingContractAreaDetailsUsers($uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $contractArea = $this->manager->getRepository(ContractArea::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $items = $this->accessHelper->getUserList(contractArea: $contractArea);

        $mappedList = array_map(callback: fn ($entity) => $self->mapEntityToDto(entity: $entity, request: ReportingUserDto::class), array: $items);

        return $this->render(view: 'management-neu/reporting/details.html.twig', parameters: $this->getUserOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_reporting_contractArea'),
            'contractArea' => $contractArea,
            'object' => ['name' => 'Vertragsgebiete'],
            'title' => 'Vertragsgebiet '.$contractArea->getName(),
            'links' => [
                [
                    'name' => 'Umschläge',
                    'path' => 'management_reporting_contractArea_details_collectingPlaces',
                    'active' => false,
                ],
                [
                    'name' => 'Benutzer',
                    'path' => 'management_reporting_contractArea_details_users',
                    'active' => true,
                ],
            ],
            'table' => [
                'name' => 'Benutzer',
                'text' => 'Hier werden alle Benutzer zum Vertragsgebiet <b>'.$contractArea->getName().'</b> angezeigt.',
            ],
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/reporting/collectingPlace', name: 'management_reporting_collectingPlace')]
    public function managementReportingCollectingPlace(): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_REPORT')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }
        $self = $this;
        $items = $this->manager->getRepository(CollectingPlace::class)->findAll();
        $mappedList = array_map(callback: fn (CollectingPlace $entity) => $self->mapEntityToDto(entity: $entity, request: ReportingCollectingPlaceDto::class), array: $items);

        return $this->render(view: 'management-neu/reporting/list.html.twig', parameters: $this->getCollectingPlaceOptions(entityList: $items, options: [
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/reporting/collectingPlace/{uuid}', name: 'management_reporting_collectingPlace_details')]
    public function managementReportingCollectingPlaceDetails($uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_REPORT')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->redirectToRoute(route: 'management_reporting_collectingPlace_details_contractArea', parameters: ['uuid' => $uuid]);
    }

    #[Route(path: '/management/reporting/collectingPlace/{uuid}/contractArea', name: 'management_reporting_collectingPlace_details_contractArea')]
    public function managementReportingCollectingPlaceDetailsContractArea($uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $collectingPlace = $this->manager->getRepository(CollectingPlace::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $items = $this->collectingPlaceHelper->getContractAreaList(collectingPlace: $collectingPlace);

        $mappedList = array_map(callback: fn ($entity) => $self->mapEntityToDto(entity: $entity, request: ReportingContractAreaDto::class), array: $items);

        $collectingPlaceName = $collectingPlace->getDsdId().' - '.$collectingPlace->getName1().' ('.$collectingPlace->getCity().')';

        return $this->render(view: 'management-neu/reporting/details.html.twig', parameters: $this->getContractAreaOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_reporting_collectingPlace'),
            'collectingPlace' => $collectingPlace,
            'object' => ['name' => 'Umschläge'],
            'title' => 'Umschlag '.$collectingPlaceName,
            'links' => [
                [
                    'name' => 'Vertragsgebiete',
                    'path' => 'management_reporting_collectingPlace_details_contractArea',
                    'active' => true,
                ],
                [
                    'name' => 'Benutzer',
                    'path' => 'management_reporting_collectingPlace_details_users',
                    'active' => false,
                ],
            ],
            'table' => [
                'name' => 'Vertragsgebiete',
                'text' => 'Hier werden alle Vertragsgebiete zum Umschlag <b>'.$collectingPlaceName.'</b> angezeigt.',
            ],
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/reporting/collectingPlace/{uuid}/users', name: 'management_reporting_collectingPlace_details_users')]
    public function managementReportingCollectingPlaceDetailsUsers($uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $collectingPlace = $this->manager->getRepository(CollectingPlace::class)->findOneBy(criteria: ['uuid' => $uuid]);
        $items = $this->accessHelper->getUserList(collectingPlace: $collectingPlace);

        $mappedList = array_map(callback: fn ($entity) => $self->mapEntityToDto(entity: $entity, request: ReportingUserDto::class), array: $items);

        $collectingPlaceName = $collectingPlace->getDsdId().' - '.$collectingPlace->getName1().' ('.$collectingPlace->getCity().')';

        return $this->render(view: 'management-neu/reporting/details.html.twig', parameters: $this->getUserOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_reporting_collectingPlace'),
            'collectingPlace' => $collectingPlace,
            'object' => ['name' => 'Umschläge'],
            'title' => 'Umschlag '.$collectingPlaceName,
            'links' => [
                [
                    'name' => 'Vertragsgebiete',
                    'path' => 'management_reporting_collectingPlace_details_contractArea',
                    'active' => false,
                ],
                [
                    'name' => 'Benutzer',
                    'path' => 'management_reporting_collectingPlace_details_users',
                    'active' => true,
                ],
            ],
            'table' => [
                'name' => 'Benutzer',
                'text' => 'Hier werden alle Benutzer zum Umschlag <b>'.$collectingPlaceName.'</b> angezeigt.',
            ],
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/reporting/user', name: 'management_reporting_user')]
    public function managementReportingUser(): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_REPORT')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }
        $self = $this;
        $items = $this->manager->getRepository(User::class)->findBy(criteria: ['deleted' => false]);
        $mappedList = array_map(callback: fn (User $entity) => $self->mapEntityToDto(entity: $entity, request: ReportingUserDto::class), array: $items);

        return $this->render(view: 'management-neu/reporting/list.html.twig', parameters: $this->getUserOptions(entityList: $items, options: [
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/reporting/user/{uuid}', name: 'management_reporting_user_details')]
    public function managementReportingUserDetails($uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_REPORT')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->redirectToRoute(route: 'management_reporting_user_details_contractArea', parameters: ['uuid' => $uuid]);
    }

    #[Route(path: '/management/reporting/user/{uuid}/contractArea', name: 'management_reporting_user_details_contractArea')]
    public function managementReportingUserDetailsContractArea($uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $user = $this->manager->getRepository(User::class)->findOneBy(criteria: ['uuid' => $uuid, 'deleted' => false]);
        $items = $this->accessHelper->getContractAreaList(user: $user);

        $mappedList = array_map(callback: fn ($entity) => $self->mapEntityToDto(entity: $entity, request: ReportingContractAreaDto::class), array: $items);

        return $this->render(view: 'management-neu/reporting/details.html.twig', parameters: $this->getContractAreaOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_reporting_user'),
            'user' => $user,
            'object' => ['name' => 'Benutzer'],
            'title' => 'Benutzer '.$user->getEmail(),
            'links' => [
                [
                    'name' => 'Vertragsgebiete',
                    'path' => 'management_reporting_user_details_contractArea',
                    'active' => true,
                ],
                [
                    'name' => 'Umschläge',
                    'path' => 'management_reporting_user_details_collectingPlaces',
                    'active' => false,
                ],
            ],
            'table' => [
                'name' => 'Vertragsgebiete',
                'text' => 'Hier werden alle Vertragsgebiete zum Benutzer <b>'.$user->getEmail().'</b> angezeigt.',
            ],
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }

    #[Route(path: '/management/reporting/user/{uuid}/collectingPlaces', name: 'management_reporting_user_details_collectingPlaces')]
    public function managementReportingUserDetailsCollectingPlaces($uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_MANAGER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $self = $this;
        $user = $this->manager->getRepository(User::class)->findOneBy(criteria: ['uuid' => $uuid, 'deleted' => false]);
        $items = $this->accessHelper->getCollectingPlaceList(user: $user);

        $mappedList = array_map(callback: fn ($entity) => $self->mapEntityToDto(entity: $entity, request: ReportingCollectingPlaceDto::class), array: $items);

        return $this->render(view: 'management-neu/reporting/details.html.twig', parameters: $this->getCollectingPlaceOptions(entityList: $items, options: [
            'backUrl' => $self->generateUrl(route: 'management_reporting_user'),
            'user' => $user,
            'object' => ['name' => 'Benutzer'],
            'title' => 'Benutzer '.$user->getEmail(),
            'links' => [
                [
                    'name' => 'Vertragsgebiete',
                    'path' => 'management_reporting_user_details_contractArea',
                    'active' => false,
                ],
                [
                    'name' => 'Umschläge',
                    'path' => 'management_reporting_user_details_collectingPlaces',
                    'active' => true,
                ],
            ],
            'table' => [
                'name' => 'Umschläge',
                'text' => 'Hier werden alle Umschläge zum Benutzer <b>'.$user->getEmail().'</b> angezeigt.',
            ],
            'list' => $this->serializer->serialize($this->sortListView(list: $mappedList, column: 'status', order: SORT_ASC), 'json', ['groups' => ['list']]),
        ]));
    }
}
