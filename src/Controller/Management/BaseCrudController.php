<?php

declare(strict_types=1);

namespace App\Controller\Management;

use App\Services\AccessHelper;
use App\Services\CollectingPlaceHelper;
use App\Services\ContractAreaHelper;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;

abstract class BaseCrudController extends AbstractController
{
    public const FORM_MODE_ADD = 'add';
    public const FORM_MODE_EDIT = 'edit';
    public const FORM_MODE_DELETE = 'delete';
    public const FORM_MODE_LOCK = 'lock';
    public const FORM_MODE_RESET = 'reset';

    public function __construct(protected ServiceEntityRepository $repository, protected SerializerInterface $serializer, protected EntityManagerInterface $entityManager, protected AccessHelper $accessHelper, protected ContractAreaHelper $contractAreaHelper, protected CollectingPlaceHelper $collectingPlaceHelper, protected string $controllerName, protected string $listTwig, protected string $detailTwig)
    {
    }

    protected function mapEntityToDto($entity, Request $request)
    {
        return $entity;
    }

    protected function mapDtoToEntity($dto, $entity, Request $request)
    {
        return $dto;
    }

    protected function getFormOptions($dto, $mode, $options): array
    {
        return match ($mode) {
            BaseCrudController::FORM_MODE_ADD => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_ADD, 'method' => Request::METHOD_POST]),
            BaseCrudController::FORM_MODE_EDIT => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_EDIT, 'method' => Request::METHOD_PUT]),
            BaseCrudController::FORM_MODE_DELETE => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_DELETE, 'method' => Request::METHOD_DELETE]),
            BaseCrudController::FORM_MODE_LOCK => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_LOCK, 'method' => Request::METHOD_POST]),
            BaseCrudController::FORM_MODE_RESET => array_merge($options, ['mode' => BaseCrudController::FORM_MODE_RESET, 'method' => Request::METHOD_POST]),
            default => $options,
        };
    }

    protected function sortListView($list, $column, $order): array
    {
        array_multisort(array_column(array: $list, column_key: $column), $order, $list);

        return $list;
    }

    protected function getListViewOptions($entityList, $options): array
    {
        return $options;
    }

    protected function getDetailViewOptions($entity, $options, $request): array
    {
        return $options;
    }

    protected function getListFindBy(Request $request)
    {
        return ['deleted' => false];
    }

    protected function getListSorting(Request $request)
    {
        return [];
    }

    protected function processList(Request $request, $newEntity, string $formType, string $redirectRoute, array $routeParams): Response
    {
        $self = $this;

        $newDto = $this->mapEntityToDto(entity: $newEntity, request: $request);

        $editForm = $this->createForm(type: $formType, data: $newDto, options: $this->getFormOptions(dto: $newDto, mode: BaseCrudController::FORM_MODE_ADD, options: []));

        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $newEntity = $this->mapDtoToEntity(dto: $newDto, entity: $newEntity, request: $request);

            $this->entityManager->persist($newEntity);
            $this->entityManager->flush();

            $routeParams['noty'] = 'success';
            $routeParams['message'] = $routeParams['object'].' wurde erfolgreich angelegt.';

            return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
        }

        $items = $this->repository->findBy(criteria: $this->getListFindBy(request: $request), orderBy: $this->getListSorting(request: $request));

        $mappedList = array_map(callback: fn (object $entity) => $self->mapEntityToDto(entity: $entity, request: $request), array: $items);

        return $this->render(view: $this->listTwig, parameters: $this->getListViewOptions(entityList: $items, options: [
            'controller_name' => $this->controllerName,
            'list' => $this->serializer->serialize($mappedList, 'json', ['groups' => ['list']]),
            'editForm' => $editForm->createView(),
        ]));
    }

    protected function processDetails($uuid, Request $request, string $editFormType, string $deleteFormType, string $lockFormType, string $resetFormType, string $redirectRoute, array $routeParams): Response
    {
        $entity = $this->repository->findOneBy(criteria: ['uuid' => $uuid]);

        if (null === $entity) {
            throw $this->createNotFoundException();
        }

        $dto = $this->mapEntityToDto(entity: $entity, request: $request);

        $editForm = $this->createForm(type: $editFormType, data: $dto, options: $this->getFormOptions(dto: $dto, mode: BaseCrudController::FORM_MODE_EDIT, options: []));

        $editForm->handleRequest($request);

        if ($editForm->isSubmitted() && $editForm->isValid()) {
            $entity = $this->mapDtoToEntity(dto: $dto, entity: $entity, request: $request);

            $this->entityManager->persist($entity);
            $this->entityManager->flush();

            $routeParams['noty'] = 'success';
            $routeParams['message'] = $routeParams['object'].' wurde erfolgreich bearbeitet.';

            return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
        }

        $deleteForm = $this->createForm(type: $deleteFormType, options: $this->getFormOptions(dto: $dto, mode: BaseCrudController::FORM_MODE_DELETE, options: []));
        $lockForm = $this->createForm(type: $lockFormType, options: $this->getFormOptions(dto: $dto, mode: BaseCrudController::FORM_MODE_LOCK, options: []));
        $resetForm = $this->createForm(type: $resetFormType, options: $this->getFormOptions(dto: $dto, mode: BaseCrudController::FORM_MODE_RESET, options: []));

        return $this->render(view: $this->detailTwig, parameters: $this->getDetailViewOptions(entity: $entity, options: [
            'controller_name' => $this->controllerName,
            'item' => $dto,
            'editForm' => $editForm->createView(),
            'deleteForm' => $deleteForm->createView(),
            'lockForm' => $lockForm->createView(),
            'resetForm' => $resetForm->createView(),
        ], request: $request));
    }

    protected function processDelete($uuid, Request $request, string $redirectRoute, array $routeParams): Response
    {
        $entity = $this->repository->findOneBy(criteria: ['uuid' => $uuid]);

        if (null === $entity) {
            throw $this->createNotFoundException();
        }

        $routeParams['noty'] = 'success';
        $routeParams['message'] = $routeParams['object'].' wurde erfolgreich gelöscht.';

        $entity->setDeleted(true);
        $this->entityManager->persist($entity);
        $this->entityManager->flush();

        return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
    }

    protected function processLock($uuid, Request $request, string $redirectRoute, array $routeParams): Response
    {
        $entity = $this->repository->findOneBy(criteria: ['uuid' => $uuid]);

        if (null === $entity) {
            throw $this->createNotFoundException();
        }

        $entity->setLocked(!$entity->getLocked());
        $this->entityManager->persist($entity);
        $this->entityManager->flush();

        $routeParams['noty'] = 'success';
        $routeParams['message'] = $routeParams['object'].' wurde erfolgreich gesperrt.';

        if (!$entity->getLocked()) {
            $routeParams['message'] = $routeParams['object'].' wurde erfolgreich entsperrt.';
        }

        return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
    }

    protected function processReset($uuid, Request $request, string $redirectRoute, array $routeParams): Response
    {
        return $this->redirectToRoute(route: $redirectRoute, parameters: $routeParams);
    }

    /*protected function createNamedForm(string $name, string $type, $data = null, array $options = [])
    {
        return $this->get('form.factory')->createNamed($name, $type, $data, $options);
    }*/
}
