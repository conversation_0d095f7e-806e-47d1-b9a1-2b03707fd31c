<?php

declare(strict_types=1);

namespace App\Controller\Api;

use App\Entity\Files\DocumentData;
use App\Entity\Main\Contract;
use App\Entity\Main\Document;
use App\Entity\Main\DocumentType;
use Doctrine\Persistence\ManagerRegistry;
use FOS\RestBundle\Controller\AbstractFOSRestController;
use Ramsey\Uuid\Uuid;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;

/**
 * Class DocumentController.
 */
class DocumentController extends AbstractFOSRestController
{
    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly ManagerRegistry $managerRegistry)
    {
    }

    #[Route(path: '/rest/{version}/file/{uuid}', name: 'file', methods: ['GET'])]
    public function index(string $uuid): Response
    {
        // get the correct entity manager documentdata in dir files
        $fileManager = $this->managerRegistry->getManager('files');

        $fileRepo = $fileManager->getRepository(DocumentData::class);

        /** @var DocumentData|null $document */
        $document = $fileRepo->findOneBy(['uuid' => $uuid]);

        if ($document) {
            $response = new StreamedResponse(callbackOrChunks: function () use ($document): void {
                $outputStream = fopen(filename: 'php://output', mode: 'wb');

                stream_copy_to_stream(from: $document->getFile(), to: $outputStream);
            });

            $response->headers->set(key: 'Content-Type', values: $document->getMimeType());

            if ('application/pdf' == $document->getMimeType()) {
                // $docType = $document->getDocumentType();
                $response->headers->set(key: 'Content-Disposition', values: 'attachment; filename="mengenmeldung-downloaded.pdf"');
            }

            return $response;
        } else {
            $this->createNotFoundException(message: 'File not found.');

            return new Response(content: 'File not found.', status: Response::HTTP_NOT_FOUND);
        }
    }

    /**
     * @example Request: curl -u api:api -X 'DELETE' localhost/file/1b6e1c78-d326-11eb-ac71-0242ac1c0004
     */
    #[Route(path: '/rest/{version}/documents/{uuid}', name: 'file-delete', methods: ['DELETE'])]
    public function delete(string $uuid): Response
    {
        $defaultManager = $this->managerRegistry->getManager('default');
        $fileManager = $this->managerRegistry->getManager('files');

        $documentDataRepo = $fileManager->getRepository(DocumentData::class);
        $fileDocument = $documentDataRepo->findOneBy(['uuid' => $uuid]);

        if ($fileDocument) {
            $fileManager->remove($fileDocument);
            $fileManager->flush();
        }

        $documentRepo = $defaultManager->getRepository(Document::class);
        $document = $documentRepo->findOneBy(['uuid' => $uuid]);

        if ($document) {
            $defaultManager->remove($document);
            $defaultManager->flush();
        }

        if (is_null(value: $fileDocument) || is_null(value: $document)) {
            return new Response(content: json_encode(value: ['uuid' => $uuid]), status: Response::HTTP_BAD_REQUEST);
        }

        return new Response(content: json_encode(value: ['uuid' => $uuid]), status: Response::HTTP_OK);
    }

    /**
     * Create Document.
     *
     * @example Request: curl -u api:api -F "file=@example.pdf" localhost/file/1b6e1c78-d326-11eb-ac71-0242ac1c0004
     * @example Request: curl -u api:api -F "file=@example.pdf" localhost/file/1b6e1c78-d326-11eb-ac71-0242ac1c0004?update=1
     */
    #[Route(path: '/rest/{version}/documents', name: 'add-document', methods: ['POST'])]
    public function addDocument(Request $request): Response
    {
        $defaultManager = $this->managerRegistry->getManager('default');
        $fileManager = $this->managerRegistry->getManager('files');

        $file = $request->files->get(key: 'file');
        if (is_null(value: $file)) {
            return $this->viewInputFileNotFound();
        }

        $data = json_decode(json: $request->get(key: 'data'), associative: true);
        if (is_null(value: $data)) {
            return $this->viewInputDataNotFound();
        }

        // check if contract already exists
        $contractPlaceRepro = $this->managerRegistry->getManager('default')->getRepository(Contract::class);
        $contract = $contractPlaceRepro->findOneBy(
            ['contractNumber' => $data['contractNumber']]
        );

        if (is_null(value: $contract)) {
            return $this->viewContractNotFound(contractNumber: $data['contractNumber']);
        }

        // check if documentType already exists
        $documentTypeRepo = $this->managerRegistry->getManager('default')->getRepository(DocumentType::class);
        $documentType = $documentTypeRepo->findOneBy(
            ['name' => $data['type']]
        );

        if (is_null(value: $documentType)) {
            return $this->viewDocumentTypeNotFound(documentType: $data['type']);
        }

        // check if document already exists
        $documentRepro = $this->managerRegistry->getManager('default')->getRepository(Document::class);
        $document = $documentRepro->findOneBy(
            [
                'documentType' => $documentType,
                'contract' => $contract,
                'number' => $data['number'],
            ]
        );

        if (!is_null(value: $document)) {
            return $this->viewDocumentExists(documentId: (string) $document->getUuid());
        }

        $document = new Document();
        $document->setUuid(uuid: Uuid::uuid4());
        $document->setNumber(number: $data['number']);
        $document->setDate(date: \DateTime::createFromFormat(format: 'Ymd', datetime: $data['date']));
        $document->setAmount(amount: $data['amount']);
        $document->setUnit(unit: $data['unit']);
        $document->setContract(contract: $contract);
        $document->setDocumentType(documentType: $documentType);
        $document->setActive(active: true);
        $document->setVisible(visible: true);
        $document->setAdditionals(additionals: array_key_exists(key: 'additional', array: $data) ? json_decode(json: $data['additional'], associative: true) : []);
        $defaultManager->persist($document);

        $fileDocument = new DocumentData();
        $fileDocument->setUuid(uuid: $document->getUuid());

        $mimeType = $file->getMimeType();
        $rawData = file_get_contents(filename: $file->getRealPath());

        $fileDocument->setMimeType(mimeType: $mimeType);
        $fileDocument->setFile(file: $rawData);
        $fileManager->persist($fileDocument);

        $defaultManager->flush();
        $fileManager->flush();

        return new Response(content: json_encode(value: ['uuid' => $document->getUuid()]), status: Response::HTTP_OK);
    }

    /**
     * Create Document.
     *
     * @example Request: curl -u api:api -F "file=@example.pdf" localhost/file/1b6e1c78-d326-11eb-ac71-0242ac1c0004
     * @example Request: curl -u api:api -F "file=@example.pdf" localhost/file/1b6e1c78-d326-11eb-ac71-0242ac1c0004?update=1
     */
    #[Route(path: '/rest/{version}/documents/{uuid}', name: 'update-document', methods: ['POST'])]
    public function updateDocument(Request $request, string $uuid): Response
    {
        $defaultManager = $this->managerRegistry->getManager('default');
        $fileManager = $this->managerRegistry->getManager('files');

        // check if document already exists
        $documentRepro = $this->managerRegistry->getManager('default')->getRepository(Document::class);
        $document = $documentRepro->findOneBy(['uuid' => $uuid]);

        if (is_null(value: $document)) {
            return $this->viewDocumentNotFound(document: $uuid);
        }

        $file = $request->files->get(key: 'file');
        if (is_null(value: $file)) {
            return $this->viewInputFileNotFound();
        }

        $data = json_decode(json: $request->get(key: 'data'), associative: true);
        if (is_null(value: $data)) {
            return $this->viewInputDataNotFound();
        }

        // check if contract already exists
        $contractPlaceRepro = $this->managerRegistry->getManager('default')->getRepository(Contract::class);
        $contract = $contractPlaceRepro->findOneBy(
            ['contractNumber' => $data['contractNumber']]
        );

        if (is_null(value: $contract)) {
            return $this->viewContractNotFound(contractNumber: $data['contractNumber']);
        }

        // check if documentType already exists
        $documentTypeRepo = $this->managerRegistry->getManager('default')->getRepository(DocumentType::class);
        $documentType = $documentTypeRepo->findOneBy(
            ['name' => $data['type']]
        );

        if (is_null(value: $documentType)) {
            return $this->viewDocumentTypeNotFound(documentType: $data['type']);
        }

        $document->setNumber(number: $data['number']);
        $document->setDate(date: \DateTime::createFromFormat(format: 'Ymd', datetime: $data['date']));
        $document->setAmount(amount: $data['amount']);
        $document->setUnit(unit: $data['unit']);
        $document->setContract(contract: $contract);
        $document->setDocumentType(documentType: $documentType);
        $document->setActive(active: true);
        $document->setVisible(visible: true);
        $document->setAdditionals(additionals: array_key_exists(key: 'additional', array: $data) ? json_decode(json: $data['additional'], associative: true) : []);
        $defaultManager->persist($document);

        // check if document data already exists
        $documentRepro = $this->managerRegistry->getManager('files')->getRepository(DocumentData::class);
        $fileDocument = $documentRepro->findOneBy(['uuid' => $uuid]);

        if (is_null(value: $fileDocument)) {
            $fileDocument = new DocumentData();
        }

        $fileDocument->setUuid(uuid: $document->getUuid());
        $mimeType = $file->getMimeType();
        $rawData = file_get_contents(filename: $file->getRealPath());

        $fileDocument->setMimeType(mimeType: $mimeType);
        $fileDocument->setFile(file: $rawData);
        $fileManager->persist($fileDocument);

        $defaultManager->flush();
        $fileManager->flush();

        return new Response(content: json_encode(value: ['uuid' => $document->getUuid()]), status: Response::HTTP_OK);
    }

    private function viewInputFileNotFound(): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: No input file!',
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewInputDataNotFound(): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: No input data!',
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewDocumentNotFound(string $document): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Document NOT found!',
                'document' => $document,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewDocumentExists(string $documentId): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Document already exists!',
                'document' => $documentId,
            ],
            statusCode: 400
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewDocumentTypeNotFound(string $documentType): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Document type NOT found!',
                'documentType' => $documentType,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }

    private function viewContractNotFound(string $contractNumber): Response
    {
        $view = $this->view(
            data: [
                'success' => false,
                'message' => 'ERROR: Contract NOT found!',
                'contractNumber' => $contractNumber,
            ],
            statusCode: 404
        );

        $view->setFormat(format: 'json');

        return $this->handleView(view: $view);
    }
}
