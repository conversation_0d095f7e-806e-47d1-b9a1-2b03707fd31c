<?php

declare(strict_types=1);

namespace App\Controller;

use App\Entity\Main\CollectingPlace;
use App\Entity\Main\ContractArea;
use App\Entity\Main\ContractAreaValidity;
use App\Entity\Main\Order;
use App\Entity\Main\User;
use App\Form\Type\CollectingEntrenceType;
use App\Form\Type\ExportType;
use App\Form\Type\OrderCollectingType;
use App\Form\Type\OrderOverviewType;
use App\Repository\Main\ContractAreaRepository;
use App\Repository\Main\ContractAreaValidityRepository;
use App\Repository\Main\OrderRepository;
use App\Repository\Main\SystemProviderRepository;
use App\Services\AccessHelper;
use App\Services\CalendarDay;
use App\Services\CalendarValidator;
use App\Services\DateTimeHelper;
use App\Services\OrderCollectCalculator;
use App\Services\OrderHelper;
use App\Services\PublicHolidayHelper;
use App\Services\WeekDayIterator;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Knp\Component\Pager\PaginatorInterface;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * Class DefaultController.
 */
class DefaultController extends AbstractController
{
    private readonly SessionInterface $session;

    /**
     * DefaultController constructor.
     */
    public function __construct(private readonly EntityManagerInterface $manager, RequestStack $requestStack, private readonly AccessHelper $accessHelper)
    {
        $this->session = $requestStack->getSession();
    }

    #[Route(path: '/')]
    public function index(Request $request): RedirectResponse
    {
        $user = $this->getUser();

        if ($user instanceof UserInterface) {
            if ($this->isGranted(attribute: 'ROLE_ADMIN')) {
                return $this->redirectToRoute(route: 'settings_dashboard');
            }

            if ($this->isGranted(attribute: 'ROLE_MANAGER')) {
                return $this->redirectToRoute(route: 'management_order_report');
            }
        }

        return $this->redirectToRoute(route: 'app_login');
    }

    /**
     * Auftragsuebersicht.
     */
    #[Template('default/order_overview.html.twig')]
    #[Route(path: '/order/overview/{dateFrom}/{dateTo}')]
    public function orderOverview(Request $request, OrderRepository $orderRepo, PaginatorInterface $paginator,
        ?\DateTime $dateFrom = null, ?\DateTime $dateTo = null): RedirectResponse|array
    {
        if (!$this->isGranted(attribute: 'ROLE_ORDER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $collectingPlace = null;
        $contractArea = null;

        /** @var User $user */
        $user = $this->getUser();

        $orderBy = $request->query->get(key: 'orderBy');
        $order = $request->query->get(key: 'order');
        $placeId = $request->query->get(key: 'place');
        $areaId = $request->query->get(key: 'area');

        // switch for ASC and DESC may also be done by paginator
        $sortOrder = ['date' => null, 'contractArea' => null, 'providerName' => null, 'id' => null, 'dsdid' => null, 'name1' => null, 'name2' => null,
            'dispoMessage' => null, 'driverMessage' => null, 'status' => null];

        $key = lcfirst(string: $orderBy ?? '');
        if (array_key_exists(key: $key, array: $sortOrder)) {
            $sortOrder[$key] = match ($order) {
                'ASC' => 'DESC',
                default => 'ASC',
            };
        } // end of switch for ASC and DESC

        if ($placeId) {
            /** @var CollectingPlace $collectingPlace */
            $collectingPlace = $this->manager->getRepository(CollectingPlace::class)->find(id: $placeId);
        }
        if ($areaId) {
            /** @var ContractArea $contractArea */
            $contractArea = $this->manager->getRepository(ContractArea::class)->find(id: $areaId);
        }

        if (is_null(value: $dateFrom)) {
            $dateFrom = new \DateTime(datetime: 'first day of this month');
        }

        if (is_null(value: $dateTo)) {
            $dateTo = new \DateTime(datetime: 'last day of this month');
        }

        /** @var CollectingPlace[] $collectingPlaces */
        $collectingPlaces = $this->accessHelper->getCollectingPlaceList(user: $user);

        $form = $this->createForm(
            type: OrderOverviewType::class, data: [],
            options: ['collectingPlace' => $collectingPlaces, 'dateFrom' => $dateFrom, 'dateTo' => $dateTo]
        );

        // init selected collecting place
        if ($collectingPlace) {
            $form->get('collectingPlace')->setData($collectingPlace);
        }

        $orderByAndOrder = null;
        if ($orderBy && $order) {
            $orderByAndOrder = $orderBy.':'.$order;
        }
        $exportForm = $this->createForm(
            type: ExportType::class,
            data: [],
            options: ['collectingPlace' => $collectingPlace ? $collectingPlace->getId() : null,
                'dateFrom' => $dateFrom,
                'dateTo' => $dateTo,
                'orderByAndOrder' => $orderByAndOrder,
                'action' => $this->generateUrl(route: 'app_download_download'),
                'method' => 'POST']
        );

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $requestParameter = [];
            $data = $form->getViewData();

            /* @var DateTime $data['dateFrom'] */
            $requestParameter['dateFrom'] = $data['dateFrom']->format('Y-m-d');

            /* @var DateTime $data['dateTo'] */
            $requestParameter['dateTo'] = $data['dateTo']->format('Y-m-d');

            if ($data['collectingPlace']) {
                $requestParameter['place'] = $data['collectingPlace']->getId();
            }

            return $this->redirectToRoute(
                route: 'app_default_orderoverview', parameters: $requestParameter);
        }

        $queryBuilder = $orderRepo->findInBetweenQuery(
            startDate: $dateFrom, endDate: $dateTo, collectingPlace: $collectingPlace, contractArea: $contractArea
        );

        $pagination = $paginator->paginate(
            $queryBuilder, // query NOT result
            $request->query->getInt(key: 'page', default: 1), // page number
            10, // limit per page
            []
        );

        return [
            'pagination' => $pagination,
            'form' => $form->createView(),
            'exportForm' => $exportForm->createView(),
            'sortOrder' => $sortOrder, // switch for ASC and DESC, maybe use paginator function instead
        ];
    }

    /**
     * Auftrag anmelden.
     */
    #[Template('default/order_new_clearance.html.twig')]
    #[Route(path: '/order/new-clearance')]
    public function orderNewClearance(Request $request, ContractAreaRepository $contractAreaRepository): array|RedirectResponse
    {
        if (!$this->isGranted(attribute: 'ROLE_ORDER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        /** @var User $user */
        $user = $this->getUser();

        $contractAreaList = $this->accessHelper->getContractAreaList(user: $user);
        $collectingPlaceList = $this->accessHelper->getCollectingPlaceList(user: $user);

        $form = $this->createForm(
            type: CollectingEntrenceType::class,
            data: [],
            options: ['collectingPlace' => $collectingPlaceList,
                'areaList' => $contractAreaList]
        );

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid() && $form->getViewData()['contract_contractArea']) {
            $data = $form->getViewData();
            $area = $contractAreaRepository->findOneBy(criteria: ['uuid' => $data['contract_contractArea']->getUuid()]);

            $this->session->set(
                'contractAreaId', $area instanceof ContractArea ? $data['contract_contractArea']->getUuid() : null
            );

            return $this->redirectToRoute(
                route: 'app_default_ordernewclearancelist', parameters: [
                    'addWeekCount' => $data['calendarWeeks'],
                    'collectingPlace' => $data['collectingPlace']->getUuid(),
                ]);
        }

        return [
            'form' => $form->createView(),
            'firstCollectingPlace' => count(value: $collectingPlaceList) > 0 ? $collectingPlaceList[0] : false,
        ];
    }

    /**
     * @param int $addWeekCount
     *
     * @throws \Exception
     */
    #[Template('default/order_new_clearance_list.html.twig')]
    #[Route(path: '/order/new-clearance-list/{collectingPlace}/{!addWeekCount}', requirements: ['addWeekCount' => '\d?'])]
    public function orderNewClearanceList(
        Request $request,
        DateTimeHelper $dateTimeHelper,
        OrderRepository $orderRepo,
        SystemProviderRepository $SystemProviderRepo,
        OrderHelper $orderHelper,
        PublicHolidayHelper $publicHolidayHelper,
        string $collectingPlace,
        $addWeekCount = 0,
    ): RedirectResponse|array {
        if (!$this->isGranted(attribute: 'ROLE_ORDER')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $collectingPlaceRepo = $this->manager->getRepository(CollectingPlace::class);
        $collectingPlace = $collectingPlaceRepo->findOneBy(criteria: ['uuid' => $collectingPlace]);

        $areaRepo = $this->manager->getRepository(ContractArea::class);
        $area = $areaRepo->findOneBy(criteria: ['uuid' => $this->session->get('contractAreaId')]);

        $user = $this->getUser();
        if (!$user instanceof User || false === $this->accessHelper->checkUserAccessCombination(user: $user, contractArea: $area, collectingPlace: $collectingPlace)) {
            return $this->redirectToRoute(route: 'app_default_ordernewclearance');
        }
        $addWeekCount = intval(value: $addWeekCount);
        $dateTime = $dateTimeHelper->addWeeks(addWeekCount: $addWeekCount);
        $week = $dateTimeHelper->getWeekFromMonToSat(dateTime: $dateTime);
        $showForm = false; // show pop up on page load
        $formDate = new \DateTime(); // date for pop up without JS initialisation

        $providers = [];
        $states = [];
        $states[] = $collectingPlace->getState()->getShortName();

        foreach ($collectingPlace->getContracts() as $contract) {
            if ($contract->getContractArea() != $area) {
                continue;
            }

            $states[] = $contract->getUnloadingPoint()->getState()->getShortName();

            foreach ($contract->getSystemProviders() as $provider) {
                $providers[] = $provider;
            }
        }

        $form = $this->createForm(
            type: OrderCollectingType::class,
            data: [],
            options: ['systemProvider' => $providers,
                'disposalNumber' => 'Wird systemseitig vergeben.',
                'collectingPlace' => $collectingPlace->getUuid()]
        );

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $values = $form->getViewData();
            $existingOrder = null;
            if (is_numeric(value: $values['disposalNumber'])) {
                $existingOrder = $orderRepo->find(id: $values['disposalNumber'] - Order::DISPOSAL_OFFSET);
            }
            is_null(value: $existingOrder) ? $orderHelper->createOrder(values: $values, area: $area) : $orderHelper->saveOrder(values: $values, order: $existingOrder, area: $area);

            // redirect to avoid caching from POST by reload
            return $this->redirectToRoute(route: 'app_default_ordernewclearancelist',
                parameters: ['addWeekCount' => $addWeekCount, 'collectingPlace' => $collectingPlace->getUuid()]
            );
        } elseif ($form->isSubmitted() && !$form->isValid()) {
            $showForm = true;
            $data = $form->getViewData();
            $formDate = new \DateTime(datetime: $data['dateTime'].' 00:00:00');
        }

        // START: validity check
        $aryPublicHoliday = $publicHolidayHelper->findAllPublicHolidayInBetween(
            countryCode: 'DE',
            startDate: $week[0]->getDtObject(),
            endDate: $week[5]->getDtObject(),
            states: $states
        );

        $collectCalculator = new OrderCollectCalculator(
            start: $week[0]->getDtObject(), end: $week[5]->getDtObject(), orderRepo: $orderRepo
        );

        $collects = $collectCalculator->getCollectOrders(collectingPlace: $collectingPlace, area: $area);

        $cancelledCollects = $collectCalculator->getCancelledCollectOrders(orders: $collects);

        /** @var ContractAreaValidityRepository $areaValidityRepo */
        $areaValidityRepo = $this->manager->getRepository(ContractAreaValidity::class);

        $validities = $areaValidityRepo->getValidities(contractArea: $area, week: $week);

        $validityOrderWeekCount = $collectCalculator->getMaximumOrderCount(
            area: $area, areaValidityRepo: $areaValidityRepo, week: $week, countOwnCollects: count(value: $collects), countOwnCancelleledCollects: count(value: $cancelledCollects)
        );

        // re-sorting of bookings for view
        $collectIterator = new WeekDayIterator();
        $collectIterator->addOrderList(orderList: $collects);

        $calValidator = new CalendarValidator(area: $area, validities: $validities, weekIterator: $collectIterator, collects: $collects, cancelledCollects: $cancelledCollects);

        /**
         * @var CalendarDay $calendarDay
         */
        foreach ($week as $calendarDay) {
            foreach ($aryPublicHoliday as $holiday) {
                if ($holiday['date']->format('Y-m-d') == $calendarDay->getDtObject()->format(format: 'Y-m-d')) {
                    $calendarDay->setisPublicHoliday(isPublicHoliday: true);
                    $calendarDay->setPublicHolidayName(publicHolidayName: $holiday['name']);
                }
            }

            $calValidator->inAreaTimeRange(day: $calendarDay);

            if ($calValidator->validityNull()) {
                $calendarDay->setIsOrderAble(isOrderAble: false);
                $calendarDay->addValidityMessage(message: 'Validity: No Validity object for Area ('.$area->getId().').');
            } else {
                $calValidator->inWeekOrderLimit(day: $calendarDay, canceledDates: count(value: $cancelledCollects));
                $calValidator->inValidityTimeRange(day: $calendarDay);
                $calValidator->checkAndSetOrderLimit(day: $calendarDay); // also sets amount of existing orders for calendarDay
                $calValidator->dayToOld(day: $calendarDay);
            }

            if ($validityOrderWeekCount - (count(value: $collects) - count(value: $cancelledCollects)) <= 0) {
                $calendarDay->setIsOrderAble(isOrderAble: false);
            }

            $calValidator->isAlreadyTransfered(day: $calendarDay);

            $calendarDay->setOrderCountMax(orderCountMax: 0);

            foreach ($validities as $validity) {
                if ($calendarDay->getDtObject()->format(format: 'Y-m-d') >= $validity->getValidFrom()->format('Y-m-d')
                        && $calendarDay->getDtObject()->format(format: 'Y-m-d') <= $validity->getValidTo()->format('Y-m-d')) {
                    $calendarDay->setOrderCountMax(orderCountMax: $validity->getAmountDay());

                    break;
                }
            }
        } // END of validity

        $earliestBookingDate = new \DateTime();
        if ($earliestBookingDate->format(format: 'N') <= 3) { // Mo - Mi -> übermorgen
            $earliestBookingDate = $earliestBookingDate->modify(modifier: 'tomorrow + 1day');
        } elseif (4 == $earliestBookingDate->format(format: 'N')) { // Do -> Montag
            $earliestBookingDate = $earliestBookingDate->modify(modifier: 'next monday');
        } else { // nächsten Dienstag
            $earliestBookingDate = $earliestBookingDate->modify(modifier: 'next tuesday');
        }

        return [
            'area' => $area,
            'orderAmountWeek' => max(0, count(value: $collects) - count(value: $cancelledCollects)),
            'orderAmountWeekLimit' => $validityOrderWeekCount,
            'form' => $form->createView(),
            'formDate' => $formDate,
            'showForm' => $showForm,
            'week' => $week,
            'collectOrders' => $collectIterator,
            'collectingPlace' => $collectingPlace,
            'addWeekCount' => $addWeekCount,
            'previousLink' => $addWeekCount > 0,
            'weekDown' => ($addWeekCount > 0) ? $addWeekCount - 1 : 0,
            'futureLink' => $addWeekCount < 6,
            'weekUp' => $addWeekCount + 1,
            'earliestBookingDate' => $earliestBookingDate,
        ];
    }

    /**
     * Allgemeine Geschaeftsbedingungen.
     */
    #[Template('default/terms_and_conditions')]
    #[Route(path: '/terms-and-conditions')]
    public function termsAndConditions(): array
    {
        return [];
    }

    /**
     * Datenschutzerklaerung.
     */
    #[Template('default/data_privacy.html.twig')]
    #[Route(path: '/data-privacy')]
    public function dataPrivacy(): array
    {
        return [];
    }

    /**
     * Impressum.
     */
    #[Template('default/imprint.html.twig')]
    #[Route(path: '/imprint')]
    public function imprint(): array
    {
        return [];
    }

    /**
     * Compliance.
     */
    #[Template('default/compliance.html.twig')]
    #[Route(path: '/compliance')]
    public function compliance(): array
    {
        return [];
    }
}
