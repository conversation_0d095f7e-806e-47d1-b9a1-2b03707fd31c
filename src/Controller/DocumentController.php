<?php

declare(strict_types=1);

namespace App\Controller;

use App\Dto\DocumentDto;
use App\Entity\Files\DocumentData;
use App\Entity\Main\Contract;
use App\Entity\Main\Document;
use App\Entity\Main\User;
use App\Form\Dto\DocumentDtoFormType;
use App\Repository\Main\ContractRepository;
use App\Repository\Main\DocumentRepository;
use App\Services\AccessHelper;
use App\Services\FileExtensionHelper;
use AutoMapperPlus\AutoMapperInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Class DefaultController.
 */
class DocumentController extends BaseCrudController
{
    private readonly EntityManagerInterface $manager;

    protected AccessHelper $accessHelper;

    /**
     * DefaultController constructor.
     */
    public function __construct(
        EntityManagerInterface $em,
        AccessHelper $accessHelper,
        SerializerInterface $serializer,
        private readonly AutoMapperInterface $autoMapper,
        DocumentRepository $repository,
        private readonly ContractRepository $contractRepository,
        private readonly ManagerRegistry $managerRegistry,
        private readonly TranslatorInterface $translator,
    ) {
        parent::__construct($repository, $serializer, $em, $accessHelper, 'DocumentController', 'document/documents_view.html.twig', 'document/documents_view.html.twig');
        $this->manager = $em;
        $this->accessHelper = $accessHelper;
        $this->serializer = $serializer;
    }

    protected function mapEntityToDto($entity, Request $request)
    {
        $self = $this;

        /** @phpstan-ignore-next-line */
        return $this->autoMapper->map($entity, DocumentDto::class, [
            'generateUrl' => fn (string $route, array $params): string => $self->generateUrl(route: $route, parameters: $params),
        ]);
    }

    protected function mapDtoToEntity($dto, $entity, Request $request)
    {
        /** @phpstan-ignore-next-line */
        return $this->autoMapper->mapToObject($dto, $entity, [
            'contractRepository' => $this->contractRepository,
        ]);
    }

    protected function getListFindBy(Request $request): array
    {
        $user = $this->getUser();
        if (!$user instanceof User) {
            return [];
        }

        $contracts = [];
        $collectingPlaceList = $this->accessHelper->getCollectingPlaceList(user: $user);

        foreach ($collectingPlaceList as $collectingPlace) {
            /** @var Contract $customerContracts */
            foreach ($collectingPlace->getContracts() as $customerContracts) {
                if ($this->accessHelper->checkUserAccessCombination(user: $user, contractArea: $customerContracts->getContractArea(), collectingPlace: $customerContracts->getCollectingPlace())) {
                    foreach ($customerContracts->getDocuments() as $customerDocuments) {
                        $contracts[] = $customerContracts;
                    }
                }
            }
        }

        return ['contract' => $contracts];
    }

    protected function getListViewOptions($entityList, $options): array
    {
        $options['columns'] = [
            'id' => ['visible' => false, 'label' => 'id'],
            'uuid' => ['visible' => false, 'label' => 'uuid'],
            'contractAreaDisplayText' => ['visible' => true, 'label' => $this->translator->trans('collectingplace.contract_area')],
            'date' => ['visible' => true, 'label' => $this->translator->trans('date')],
            'number' => ['visible' => true, 'label' => $this->translator->trans('weighing_slip_number')],
            'amount' => ['visible' => true, 'label' => $this->translator->trans('net_weight')],
            'unit' => ['visible' => true, 'label' => $this->translator->trans('unit')],
            'detailLink' => ['visible' => false],
        ];

        return $options;
    }

    #[Route(path: '/documents/list', name: 'app_default_documentsview', methods: ['GET'])]
    public function documentsView(Request $request): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_DOCUMENTS')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        return $this->processList(request: $request, newEntity: new Document(), formType: DocumentDtoFormType::class, redirectRoute: 'document', routeParams: []);
    }

    #[Route(path: '/file/{uuid}', name: 'documents_file', methods: ['GET'])]
    public function fileView(string $uuid): Response
    {
        if (!$this->isGranted(attribute: 'ROLE_DOCUMENTS')) {
            return $this->redirectToRoute(route: 'app_default_index');
        }

        $user = $this->getUser();
        if (!$user instanceof User) {
            throw $this->createAccessDeniedException();
        }
        $collectingPlaces = $this->accessHelper->getCollectingPlaceList(user: $user);

        $document = $this->manager->getRepository(Document::class)->findOneBy(criteria: ['uuid' => $uuid]);

        /** @var DocumentData|null $file */
        $file = $this->managerRegistry
            ->getManager('files')
            ->getRepository(DocumentData::class)
            ->findOneBy(['uuid' => $uuid]);

        if (!in_array(needle: $document->getContract()->getCollectingPlace(), haystack: $collectingPlaces)) {
            $this->createNotFoundException(message: 'File not found.');

            return new Response(content: 'File not found.', status: Response::HTTP_NOT_FOUND);
        }

        if ($file) {
            $response = new StreamedResponse(callbackOrChunks: function () use ($file): void {
                $outputStream = fopen(filename: 'php://output', mode: 'wb');

                stream_copy_to_stream(from: $file->getFile(), to: $outputStream);
            });

            $response->headers->set(key: 'Content-Type', values: $file->getMimeType());

            $type = $document->getDocumentType()->getName();
            $name = $document->getNumber();
            $contract = $document->getContract()->getContractNumber();

            // $name = preg_replace('/\.\w{2,5}$/', '', $file->getFileName());

            $fileExtensionHelper = new FileExtensionHelper();

            $fileExtension = $fileExtensionHelper->getFileExtensionByMimeType(mimeType: $file->getMimeType());

            $disposition = HeaderUtils::makeDisposition(
                disposition: HeaderUtils::DISPOSITION_ATTACHMENT,
                filename: sprintf('%s-%s-%s%s', $contract, $type, $name, $fileExtension)
            );

            $response->headers->set(key: 'Content-Disposition', values: $disposition);

            return $response;
        } else {
            $this->createNotFoundException(message: 'File not found.');

            return new Response(content: 'File not found.', status: Response::HTTP_NOT_FOUND);
        }

        // return $this->processList($request, new Document(), DocumentDtoFormType::class, 'document', []);
    }
}
