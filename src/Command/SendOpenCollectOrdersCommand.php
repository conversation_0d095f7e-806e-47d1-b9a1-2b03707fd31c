<?php

declare(strict_types=1);

namespace App\Command;

use App\Entity\Main\Order;
use App\Repository\Main\OrderRepository;
use App\Services\Mailer;
use App\Services\OrderHelper;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\Argument;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:sendOpenCollectOrders',
    description: 'Checks on CollectOrders which are not transferred.',
)]
class SendOpenCollectOrdersCommand
{
    public function __construct(protected OrderHelper $orderHelper, protected EntityManagerInterface $manager, private readonly Mailer $mailer)
    {
    }

    public function __invoke(InputInterface $input, #[Argument(name: 'addWeek', description: '0 Starts from this or last Monday until +1 Year from next Sunday, 1 Starts from next Monday +1 Year from next Sunday')]
        ?string $addWeek, OutputInterface $output): int
    {
        $io = new SymfonyStyle(input: $input, output: $output);

        $addWeeksCount = $addWeek;

        $dtStart = new \DateTime();
        $dtEnd = new \DateTime();

        if (0 != $addWeeksCount) {
            $dtStart->modify(modifier: '+'.$addWeeksCount.' week');
            $dtEnd->modify(modifier: '+'.$addWeeksCount.' week');
        }

        // 'N' = 1 is Mon, 'N' = 7 is Sun
        if (1 != $dtStart->format(format: 'N')) {
            $dtStart->modify(modifier: 'last Monday');
        }

        // 'N' = 1 is Mon, 'N' = 7 is Sun
        if (7 != $dtStart->format(format: 'N')) {
            $dtEnd->modify(modifier: 'next Sunday');
            $dtEnd->modify(modifier: '+ 1 year');
        }

        $io->writeln(messages: 'Getting orders from '.$dtStart->format(format: 'D d.m.Y').' to '.$dtEnd->format(format: 'D d.m.Y'));

        /** @var OrderRepository $orderRepo */
        $orderRepo = $this->manager->getRepository(Order::class);

        /** @var Order[] $orderList */
        $orderList = $orderRepo->findTransferOpenInBetween(startDate: $dtStart, endDate: $dtEnd);
        $transferErrors = [];

        foreach ($orderList as $order) {
            $io->writeln(
                messages: 'Order Id: '.$order->getId().' Date '.$order->getDate()->format(format: 'D d.m.Y').
                ' Collect Place Name: '.$order->getCollectingPlace()->getName1()
            );

            $sendErrors = $this->orderHelper->send(order: $order);

            if (count(value: $sendErrors) > 0) {
                foreach ($sendErrors as $error) {
                    $io->writeln(messages: '<fg=black;bg=red>Error during transfer: '.$error.'</>');
                }
                $transferErrors[Order::DISPOSAL_OFFSET + $order->getId()] = $sendErrors;
            } else {
                $io->writeln(messages: $order->getId().'Successful Transfer to SAP.');
            }
        }

        if (0 < count(value: $transferErrors)) {
            if ((date(format: 'Hi') > '0956' && date(format: 'Hi') < '1004') || (date(format: 'Hi') > '1356' && date(format: 'Hi') < '1404')) {
                $this->mailer->sendErrorMessages(errors: $transferErrors);
            }

            $io->writeln(messages: '<fg=black;bg=red>Not all order requests have been transferred, please check error messages and order states!</>');
        }

        $io->success(message: 'app:sendOpenCollectOrders');

        return 0;
    }
}
