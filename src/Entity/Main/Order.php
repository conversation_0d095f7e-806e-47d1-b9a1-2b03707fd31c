<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Main\OrderRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

/**
 * Class Order (Bestellung // AbholungsAnmeldung).
 */
#[ORM\Table(name: 'collect_order')]
#[ORM\Entity(repositoryClass: OrderRepository::class)]
class Order implements HasUuid, HasCreateModifyStamps
{
    use CommonTrait;

    public const DISPOSAL_OFFSET = 1000000;

    public const STATUS_TRANSMISSION_READY = 'R';

    public const STATUS_TRANSMISSION_ERROR = 'E';

    public const STATUS_TRANSMISSION_SUCCESS = 'S';

    #[ORM\Column(name: 'order_id', type: Types::STRING, length: 255, nullable: false)]
    protected string $orderId;

    /**
     * @var \DateTime
     */
    #[ORM\Column(name: 'date', type: Types::DATETIME_MUTABLE, nullable: false)]
    protected \DateTimeInterface $date;

    #[ORM\Column(name: 'transfered', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $transfered = null;

    #[ORM\Column(name: 'canceled', type: Types::DATETIME_MUTABLE, nullable: true)]
    protected ?\DateTimeInterface $canceled = null;

    #[ORM\Column(name: 'transfer_status', type: Types::STRING, length: 1, nullable: true)]
    protected ?string $transferStatus = null;

    #[ORM\ManyToOne(targetEntity: SystemProvider::class)]
    #[ORM\JoinColumn(name: 'system_provider', referencedColumnName: 'id', nullable: true)]
    protected ?SystemProvider $systemProvider = null;

    #[ORM\Column(name: 'driver_message', type: Types::STRING, length: 255, nullable: true)]
    protected ?string $driverMessage = null;

    #[ORM\Column(name: 'dispo_message', type: Types::STRING, length: 255, nullable: true)]
    protected ?string $dispoMessage = null;

    #[ORM\ManyToOne(targetEntity: CollectingPlace::class)]
    #[ORM\JoinColumn(name: 'collecting_place', referencedColumnName: 'id', nullable: false)]
    protected ?CollectingPlace $collectingPlace = null;

    #[ORM\ManyToOne(targetEntity: UnloadingPoint::class)]
    #[ORM\JoinColumn(name: 'unloading_point', referencedColumnName: 'id', nullable: false)]
    protected ?UnloadingPoint $unloadingPoint = null;

    #[ORM\ManyToOne(targetEntity: ContractArea::class, inversedBy: 'orders')]
    #[ORM\JoinColumn(name: 'contract_area_id', referencedColumnName: 'id', nullable: false)]
    private ?ContractArea $contractArea = null;

    #[ORM\ManyToOne(targetEntity: Contract::class, inversedBy: 'orders')]
    #[ORM\JoinColumn(name: 'contract_id', referencedColumnName: 'id', nullable: false)]
    private ?Contract $contract = null;

    public function getSystemProvider(): ?SystemProvider
    {
        return $this->systemProvider;
    }

    public function setSystemProvider(SystemProvider $systemProvider): Order
    {
        $this->systemProvider = $systemProvider;

        return $this;
    }

    public function getOrderId(): string
    {
        return $this->orderId;
    }

    public function setOrderId(string $orderId): Order
    {
        $this->orderId = $orderId;

        return $this;
    }

    public function getDate(): \DateTime
    {
        return $this->date;
    }

    public function setDate(\DateTime $date): Order
    {
        $this->date = $date;

        return $this;
    }

    public function getTransfered(): ?\DateTimeInterface
    {
        return $this->transfered;
    }

    public function setTransfered(\DateTime $transfered): Order
    {
        $this->transfered = $transfered;

        return $this;
    }

    public function getCanceled(): ?\DateTimeInterface
    {
        return $this->canceled;
    }

    public function setCanceled(?\DateTime $canceled): Order
    {
        $this->canceled = $canceled;

        return $this;
    }

    public function getTransferStatus(): ?string
    {
        return $this->transferStatus;
    }

    public function setTransferStatus(string $transferStatus): Order
    {
        $this->transferStatus = $transferStatus;

        return $this;
    }

    public function getDriverMessage(): ?string
    {
        return $this->driverMessage;
    }

    public function setDriverMessage(string $driverMessage): Order
    {
        $this->driverMessage = $driverMessage;

        return $this;
    }

    public function getDispoMessage(): ?string
    {
        return $this->dispoMessage;
    }

    public function setDispoMessage(string $dispoMessage): Order
    {
        $this->dispoMessage = $dispoMessage;

        return $this;
    }

    public function getCollectingPlace(): CollectingPlace
    {
        return $this->collectingPlace;
    }

    public function setCollectingPlace(CollectingPlace $collectingPlace): Order
    {
        $this->collectingPlace = $collectingPlace;

        return $this;
    }

    public function getUnloadingPoint(): UnloadingPoint
    {
        return $this->unloadingPoint;
    }

    public function setUnloadingPoint(UnloadingPoint $unloadingPoint): Order
    {
        $this->unloadingPoint = $unloadingPoint;

        return $this;
    }

    public function getContractArea(): ?ContractArea
    {
        return $this->contractArea;
    }

    public function setContractArea(?ContractArea $contractArea): self
    {
        $this->contractArea = $contractArea;

        return $this;
    }

    public function getContract(): ?Contract
    {
        return $this->contract;
    }

    public function setContract(?Contract $contract): self
    {
        $this->contract = $contract;

        return $this;
    }

    public function getDisposalNumber(): int
    {
        return $this->id + self::DISPOSAL_OFFSET;
    }
}
