<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Main\ContractAreaValidityRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ContractAreaValidityRepository::class)]
class ContractAreaValidity implements HasUuid, HasCreateModifyStamps, \Stringable
{
    use CommonTrait;

    #[ORM\Column(type: Types::STRING, length: 255)]
    private ?string $validityId = null;

    #[ORM\Column(type: Types::INTEGER)]
    private ?int $amountDay = null;

    #[ORM\Column(type: Types::INTEGER)]
    private ?int $amountWeek = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $validFrom = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $validTo = null;

    #[ORM\ManyToOne(targetEntity: ContractArea::class, inversedBy: 'contractAreaValidities')]
    #[ORM\JoinColumn(name: 'contract_area', referencedColumnName: 'id', nullable: true)]
    private ?ContractArea $contractArea = null;

    public function getValidityId(): ?string
    {
        return $this->validityId;
    }

    public function setValidityId(string $validityId): self
    {
        $this->validityId = $validityId;

        return $this;
    }

    public function getAmountDay(): ?int
    {
        return $this->amountDay;
    }

    public function setAmountDay(int $amountDay): self
    {
        $this->amountDay = $amountDay;

        return $this;
    }

    public function getAmountWeek(): ?int
    {
        return $this->amountWeek;
    }

    public function setAmountWeek(int $amountWeek): self
    {
        $this->amountWeek = $amountWeek;

        return $this;
    }

    public function getValidFrom(): ?\DateTimeInterface
    {
        return $this->validFrom;
    }

    public function setValidFrom(\DateTimeInterface $validFrom): self
    {
        $this->validFrom = $validFrom;

        return $this;
    }

    public function getValidTo(): ?\DateTimeInterface
    {
        return $this->validTo;
    }

    public function setValidTo(\DateTimeInterface $validTo): self
    {
        $this->validTo = $validTo;

        return $this;
    }

    public function getContractArea(): ?ContractArea
    {
        return $this->contractArea;
    }

    public function setContractArea(?ContractArea $contractArea): self
    {
        $this->contractArea = $contractArea;

        return $this;
    }

    public function __toString(): string
    {
        return strval(value: $this->validFrom->format('d.m.Y').' bis '.$this->validTo->format('d.m.Y').' - Tag: '.$this->amountDay.', Woche: '.$this->amountWeek);
    }
}
