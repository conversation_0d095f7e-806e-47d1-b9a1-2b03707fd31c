<?php

declare(strict_types=1);

namespace App\Entity\Main;

use App\Entity\Common\CommonTrait;
use App\Entity\Common\HasCreateModifyStamps;
use App\Entity\Common\HasUuid;
use App\Repository\Main\ContractRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;

/**
 * Class Contract (Vertrag).
 */
#[ORM\Table(name: 'contract')]
#[ORM\Entity(repositoryClass: ContractRepository::class)]
class Contract implements HasUuid, HasCreateModifyStamps, \Stringable
{
    use CommonTrait;

    #[ORM\Column(name: 'contract_id', type: Types::STRING, length: 255, nullable: false)]
    #[Groups(['list'])]
    protected string $contractId;

    #[ORM\Column(name: 'contract_number', type: Types::STRING, length: 10, nullable: false)]
    protected string $contractNumber;

    /**
     * @var \DateTime
     */
    #[ORM\Column(name: 'valid_from', type: Types::DATETIME_MUTABLE, nullable: false)]
    protected \DateTimeInterface $validFrom;

    /**
     * @var \DateTime
     */
    #[ORM\Column(name: 'valid_to', type: Types::DATETIME_MUTABLE, nullable: false)]
    protected \DateTimeInterface $validTo;

    /**
     * @var Collection<int, Order>
     */
    #[ORM\OneToMany(targetEntity: Order::class, mappedBy: 'contract')]
    private Collection $orders;

    #[ORM\ManyToOne(targetEntity: CollectingPlace::class, inversedBy: 'contracts')]
    #[ORM\JoinColumn(nullable: true)]
    private ?CollectingPlace $collectingPlace = null;

    #[ORM\ManyToOne(targetEntity: UnloadingPoint::class, inversedBy: 'contracts')]
    #[ORM\JoinColumn(nullable: true)]
    private ?UnloadingPoint $unloadingPoint = null;

    #[ORM\ManyToOne(targetEntity: ContractArea::class, inversedBy: 'contracts')]
    private ?ContractArea $contractArea = null;

    /**
     * @var Collection<int, SystemProvider>
     */
    #[ORM\ManyToMany(targetEntity: SystemProvider::class, inversedBy: 'contracts')]
    private Collection $systemProviders;

    /**
     * @var Collection<int, Document>
     */
    #[ORM\OneToMany(targetEntity: Document::class, mappedBy: 'contract')]
    private Collection $documents;

    public function __construct()
    {
        $this->orders = new ArrayCollection();
        $this->systemProviders = new ArrayCollection();
        $this->documents = new ArrayCollection();
    }

    public function getContractId(): string
    {
        return $this->contractId;
    }

    public function setContractId(string $contractId): Contract
    {
        $this->contractId = $contractId;

        return $this;
    }

    public function getContractNumber(): string
    {
        return $this->contractNumber;
    }

    public function setContractNumber(string $contractNumber): Contract
    {
        $this->contractNumber = $contractNumber;

        return $this;
    }

    public function getValidFrom(): \DateTime
    {
        return $this->validFrom;
    }

    public function setValidFrom(\DateTime $validFrom): Contract
    {
        $this->validFrom = $validFrom;

        return $this;
    }

    public function getValidTo(): \DateTime
    {
        return $this->validTo;
    }

    public function setValidTo(\DateTime $validTo): Contract
    {
        $this->validTo = $validTo;

        return $this;
    }

    public function getCollectingPlace(): ?CollectingPlace
    {
        return $this->collectingPlace;
    }

    public function setCollectingPlace(?CollectingPlace $collectingPlace): self
    {
        $this->collectingPlace = $collectingPlace;

        return $this;
    }

    public function getUnloadingPoint(): ?UnloadingPoint
    {
        return $this->unloadingPoint;
    }

    public function setUnloadingPoint(?UnloadingPoint $unloadingPoint): self
    {
        $this->unloadingPoint = $unloadingPoint;

        return $this;
    }

    public function getContractArea(?\DateTime $dateTime = null): ?ContractArea
    {
        if (is_null(value: $dateTime)) {
            return $this->contractArea;
        }

        if ($this->contractArea->getValidTo() > $dateTime) {
            if ($this->contractArea->getValidFrom() < $dateTime) {
                return $this->contractArea;
            }

            return null;
        }

        return null;
    }

    public function setContractArea(?ContractArea $contractArea): self
    {
        $this->contractArea = $contractArea;

        return $this;
    }

    /**
     * @return Collection<int, SystemProvider>
     */
    public function getSystemProviders(): Collection
    {
        return $this->systemProviders;
    }

    public function addSystemProvider(SystemProvider $systemProvider): self
    {
        if (!$this->systemProviders->contains(element: $systemProvider)) {
            $this->systemProviders->add(element: $systemProvider);
        }

        return $this;
    }

    public function removeSystemProvider(SystemProvider $systemProvider): self
    {
        if ($this->systemProviders->contains(element: $systemProvider)) {
            $this->systemProviders->removeElement(element: $systemProvider);
        }

        return $this;
    }

    /**
     * @return Collection<int, Order>
     */
    public function getOrders(): Collection
    {
        return $this->orders;
    }

    public function addOrder(Order $order): self
    {
        if (!$this->orders->contains($order)) {
            $this->orders->add($order);
            $order->setContract(contract: $this);
        }

        return $this;
    }

    public function removeOrder(Order $order): self
    {
        if ($this->orders->contains($order)) {
            $this->orders->removeElement($order);
            // set the owning side to null (unless already changed)
            if ($order->getContract() === $this) {
                $order->setContract(contract: null);
            }
        }

        return $this;
    }

    public function __toString(): string
    {
        return strval(value: $this->contractNumber);
    }

    /**
     * @return Collection<int, Document>
     */
    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    public function addDocument(Document $document): self
    {
        if (!$this->documents->contains(element: $document)) {
            $this->documents->add(element: $document);
            $document->setContract(contract: $this);
        }

        return $this;
    }

    public function removeDocument(Document $document): self
    {
        // set the owning side to null (unless already changed)
        if ($this->documents->removeElement(element: $document) && $document->getContract() === $this) {
            $document->setContract(contract: null);
        }

        return $this;
    }
}
