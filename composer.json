{"name": "prezero/lvp-portal", "description": "LVP-Portal Symfony application", "type": "project", "license": "proprietary", "require": {"php": "^8.4", "ext-bcmath": "*", "ext-calendar": "*", "ext-ctype": "*", "ext-curl": "*", "ext-gd": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-mysqli": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-sockets": "*", "ext-xsl": "*", "ext-zip": "*", "composer/package-versions-deprecated": "^1.11", "doctrine/dbal": "^4", "doctrine/doctrine-bundle": "^2.13", "doctrine/doctrine-migrations-bundle": "^3.4", "doctrine/orm": "^3.3", "dragonmantank/cron-expression": "^3.3", "easycorp/easyadmin-bundle": "^4.8", "fisharebest/ext-calendar": "~2.5", "friendsofsymfony/rest-bundle": "^3.6", "guzzlehttp/guzzle": "^7.8", "knplabs/knp-paginator-bundle": "^6.8", "liip/monitor-bundle": "^2.24", "mark-gerarts/automapper-plus-bundle": "^1.4", "monolog/monolog": "^3.8", "nelmio/api-doc-bundle": "^4.19", "phpdocumentor/reflection-docblock": "^5.3", "phpoffice/phpspreadsheet": "^1.29", "ramsey/uuid": "^4.7", "ramsey/uuid-doctrine": "^2.1", "symfony/apache-pack": "^1.0", "symfony/asset": "7.3.*", "symfony/console": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/expression-language": "7.3.*", "symfony/flex": "^2.4", "symfony/form": "7.3.*", "symfony/framework-bundle": "7.3.*", "symfony/http-client": "7.3.*", "symfony/intl": "7.3.*", "symfony/lock": "7.3.*", "symfony/mailer": "7.3.*", "symfony/messenger": "7.3.*", "symfony/monolog-bundle": "^3.10", "symfony/process": "7.3.*", "symfony/property-access": "7.3.*", "symfony/property-info": "7.3.*", "symfony/rate-limiter": "7.3.*", "symfony/runtime": "7.3.*", "symfony/scheduler": "7.3.*", "symfony/security-bundle": "7.3.*", "symfony/serializer": "7.3.*", "symfony/translation": "7.3.*", "symfony/twig-bundle": "7.3.*", "symfony/ux-twig-component": "^2.26", "symfony/validator": "7.3.*", "symfony/web-link": "7.3.*", "symfony/webpack-encore-bundle": "^2.1", "symfony/yaml": "7.3.*", "twig/extra-bundle": "^3.8", "twig/twig": "^2.12|^3.8", "zircote/swagger-php": "^4.8"}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.5", "friendsofphp/php-cs-fixer": "^3.49", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/phpstan": "^2.1", "phpstan/phpstan-symfony": "^2.0", "phpunit/phpunit": "^12.4", "rector/rector": "*", "savinmikhail/add_named_arguments_rector": "^0.1.20", "symfony/browser-kit": "7.3.*", "symfony/css-selector": "7.3.*", "symfony/debug-bundle": "7.3.*", "symfony/maker-bundle": "^1.53", "symfony/phpunit-bridge": "^7.3", "symfony/stopwatch": "7.3.*", "symfony/web-profiler-bundle": "7.3.*"}, "config": {"optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"symfony/flex": true, "phpro/grumphp": true, "symfony/runtime": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"paragonie/random_compat": "2.*", "symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php71": "*", "symfony/polyfill-php70": "*", "symfony/polyfill-php56": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"], "phpstan": "phpstan analyse --configuration=phpstan.dist.neon"}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*", "docker": true}}}